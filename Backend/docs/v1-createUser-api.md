# Create User V1 API Documentation

## Endpoint
`POST /emailservice/v1/createUser`

## Description
Creates a new user in Microsoft Graph using the SCIM bulk upload API with synchronization service. This endpoint takes user data in a Java Map format and creates a payload that Microsoft Graph's bulk upload service will accept.

## Request Body
The API accepts a `CreateUserRequestDto` object with the following structure:

```json
{
  "externalId": "emp67890",
  "userName": "<EMAIL>",
  "givenName": "Aisha",
  "familyName": "Khan",
  "displayName": "<PERSON><PERSON> Khan",
  "nickName": "aisha.k",
  "userType": "Contractor",
  "title": "Data Analyst",
  "preferredLanguage": "en-IN",
  "locale": "en-IN",
  "timezone": "Asia/Kolkata",
  "active": true,
  "primaryEmail": "<EMAIL>",
  "emailType": "work",
  "phoneNumbers": [
    {
      "value": "+919876543210",
      "type": "mobile"
    },
    {
      "value": "+911234567890",
      "type": "work"
    }
  ],
  "address": {
    "type": "work",
    "streetAddress": "45 MG Road",
    "locality": "Bangalore",
    "region": "Karnataka",
    "postalCode": "560001",
    "country": "IN",
    "primary": false
  },
  "employeeNumber": "emp67890",
  "organization": "Visionwaves",
  "department": "Analytics",
  "division": "Data Science",
  "costCenter": "CC-002",
  "managerValue": "31ad6133-6060-4cfa-9cc3-952fbc26ff98",
  "hireDate": "2025-07-01",
  "jobCode": "SWE-2"
}
```

## Configuration
The following properties are configured in `application.properties`:

- `graph.service.principal.id`: Service Principal ID for Microsoft Graph synchronization
- `graph.synchronization.job.id`: Synchronization Job ID for bulk upload

## Static Values
The following values are automatically added from configuration:
- SCIM schemas (core, enterprise, and VisionWaves extensions)
- Service Principal ID and Synchronization Job ID
- Bulk operation method and path

## Response
Returns a Map with the following structure:
```json
{
  "result": "success|failed",
  "status": 200,
  "body": "Microsoft Graph API response",
  "error": "Error message if failed"
}
```

## Example cURL
```bash
curl --location 'http://localhost:8088/emailagent/emailservice/v1/createUser' \
--header 'Content-Type: application/json' \
--header 'Authorization: Bearer YOUR_TOKEN' \
--data '{
  "externalId": "emp67890",
  "userName": "<EMAIL>",
  "givenName": "Aisha",
  "familyName": "Khan",
  "displayName": "Aisha Khan",
  "nickName": "aisha.k",
  "userType": "Contractor",
  "title": "Data Analyst",
  "preferredLanguage": "en-IN",
  "locale": "en-IN",
  "timezone": "Asia/Kolkata",
  "active": true,
  "primaryEmail": "<EMAIL>",
  "emailType": "work",
  "phoneNumbers": [
    {
      "value": "+919876543210",
      "type": "mobile"
    }
  ],
  "employeeNumber": "emp67890",
  "organization": "Visionwaves",
  "department": "Analytics",
  "hireDate": "2025-07-01",
  "jobCode": "SWE-2"
}'
```
