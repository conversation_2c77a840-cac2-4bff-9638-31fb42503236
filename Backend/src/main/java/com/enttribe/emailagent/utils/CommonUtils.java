package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.constant.EmailConstants;
import com.enttribe.emailagent.dto.MeetingWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.fasterxml.jackson.databind.node.BooleanNode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * A utility class providing common functionality for meeting and event management operations. This
 * class contains methods for converting meeting data to various JSON formats required by external
 * APIs, particularly for Microsoft Graph integration.
 *
 * <p>The class provides utilities for:
 *
 * <ul>
 *   <li>Converting meeting requests to JSON payloads for creating and updating events
 *   <li>Handling attendee management (required and optional)
 *   <li>Managing meeting scheduling and rescheduling operations
 *   <li>Processing location and online meeting configurations
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0.0
 */
@Slf4j
public final class CommonUtils {

  private static final ObjectMapper objectMapper = new ObjectMapper();

  private CommonUtils() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }

  public static String convertToMeetingRequestForUpdateEvent(Map<String, Object> jsonBody) {
    String timeZone = (String) jsonBody.get(EmailConstants.TIME_ZONE);

    ObjectNode resultJson = objectMapper.createObjectNode();

    addSubjectToJson(resultJson, jsonBody);
    addBodyToJson(resultJson, jsonBody);
    addStartTimeToJson(resultJson, jsonBody, timeZone);
    addEndTimeToJson(resultJson, jsonBody, timeZone);
    addAttendeesToJson(resultJson, jsonBody);
    addMeetingTypeToJson(resultJson, jsonBody);
    addLocationToJson(resultJson, jsonBody);
    addRecurrenceToJson(resultJson, jsonBody);

    return resultJson.toString();
  }

  private static void addRecurrenceToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    Object recurrenceObj = jsonBody.get("recurrence");
    if (recurrenceObj instanceof Map) {
      log.debug("Recurrence object: {}", recurrenceObj);
      ObjectNode recurrenceJson = objectMapper.convertValue(recurrenceObj, ObjectNode.class);
      resultJson.set("recurrence", recurrenceJson);
    }
    log.debug("UpdateEvent json : {}", resultJson);
  }

  private static void addSubjectToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    String subject = (String) jsonBody.get(EmailConstants.SUBJECT);
    if (subject != null && !subject.trim().isEmpty()) {
      resultJson.set(EmailConstants.SUBJECT, TextNode.valueOf(subject));
    }
  }

  private static void addBodyToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    String body = (String) jsonBody.get(EmailConstants.BODY);
    if (body != null && !body.trim().isEmpty()) {
      ObjectNode bodyJson = objectMapper.createObjectNode();
      bodyJson.set("contentType", TextNode.valueOf(EmailConstants.HTML));
      bodyJson.set(EmailConstants.CONTENT, TextNode.valueOf(body));
      resultJson.set(EmailConstants.BODY, bodyJson);
    }
  }

  private static void addStartTimeToJson(ObjectNode resultJson, Map<String, Object> jsonBody, String timeZone) {
    String startTime = (String) jsonBody.get(EmailConstants.MEETING_START_TIME);
    if (startTime != null && !startTime.trim().isEmpty()) {
      ObjectNode startJson = objectMapper.createObjectNode();
      startJson.set(EmailConstants.DATE_TIME, TextNode.valueOf(startTime));
      startJson.set(EmailConstants.TIME_ZONE, TextNode.valueOf(timeZone));
      resultJson.set(EmailConstants.START, startJson);
    }
  }

  private static void addEndTimeToJson(ObjectNode resultJson, Map<String, Object> jsonBody, String timeZone) {
    String endTime = (String) jsonBody.get(EmailConstants.MEETING_END_TIME);
    if (endTime != null && !endTime.trim().isEmpty()) {
      ObjectNode endJson = objectMapper.createObjectNode();
      endJson.set(EmailConstants.DATE_TIME, TextNode.valueOf(endTime));
      endJson.set(EmailConstants.TIME_ZONE, TextNode.valueOf(timeZone));
      resultJson.set(EmailConstants.END, endJson);
    }
  }

  private static void addAttendeesToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    ArrayNode attendeesJsonArray = objectMapper.createArrayNode();
    List<String> requiredAttendees = (List<String>) jsonBody.get(EmailConstants.REQUIRED_ATTENDEES);
    List<String> optionalAttendees = new ArrayList<>();
    if (jsonBody.containsKey(EmailConstants.OPTIONAL_ATTENDEES)) {
      optionalAttendees = (List<String>) jsonBody.get(EmailConstants.OPTIONAL_ATTENDEES);
    }

    if (requiredAttendees != null && !requiredAttendees.isEmpty()) {
      List<MeetingWrapper> meetingWrappers = new ArrayList<>();
      setAttendees(meetingWrappers, requiredAttendees, optionalAttendees);

      meetingWrappers.forEach((MeetingWrapper meetingWrapper) -> {
        ObjectNode attendeeJson = objectMapper.createObjectNode();
        ObjectNode emailAddressJson = objectMapper.createObjectNode();
        emailAddressJson.set(EmailConstants.ADDRESS, TextNode.valueOf(meetingWrapper.getEmail()));
        attendeeJson.set(EmailConstants.EMAIL_ADDRESS, emailAddressJson);
        attendeeJson.set(EmailConstants.TYPE, TextNode.valueOf(meetingWrapper.getType()));
        attendeesJsonArray.add(attendeeJson);
      });

      resultJson.set(EmailConstants.ATTENDEES, attendeesJsonArray);
    }
  }

  private static void addMeetingTypeToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    String meetingType = (String) jsonBody.get(EmailConstants.MEETING_TYPE);
    if (meetingType != null && meetingType.equals(EmailConstants.TEAMS)) {
      resultJson.set(EmailConstants.IS_ONLINE_MEETING, BooleanNode.TRUE);
      resultJson.set(EmailConstants.ONLINE_MEETING_PROVIDER, TextNode.valueOf(EmailConstants.TEAMS_FOR_BUSINESS));
    }
  }

  private static void addLocationToJson(ObjectNode resultJson, Map<String, Object> jsonBody) {
    String location = (String) jsonBody.get(EmailConstants.LOCATION);
    String locationUrl = (String) jsonBody.get(EmailConstants.LOCATION_URL);
    if (location != null) {
      ObjectNode locationObj = objectMapper.createObjectNode();
      locationObj.set(EmailConstants.DISPLAY_NAME, TextNode.valueOf(location));
      Optional.ofNullable(locationUrl)
          .ifPresent((String url) -> locationObj.set(EmailConstants.LOCATION_URL, TextNode.valueOf(url)));
      resultJson.set(EmailConstants.LOCATION, locationObj);
    }
  }

  public static String convertToMeetingRequestJson(Map<String, Object> inputMap, String emailId) {
    String timeZone = (String) inputMap.get(EmailConstants.TIME_ZONE);

    ObjectNode resultJson = objectMapper.createObjectNode();

    // Subject
    resultJson.set(EmailConstants.SUBJECT, TextNode.valueOf((String) inputMap.get(EmailConstants.SUBJECT)));

    // Body
    ObjectNode bodyJson = objectMapper.createObjectNode();
    bodyJson.set("contentType", TextNode.valueOf(EmailConstants.HTML));
    bodyJson.set(EmailConstants.CONTENT, TextNode.valueOf((String) inputMap.get(EmailConstants.BODY)));
    resultJson.set(EmailConstants.BODY, bodyJson);

    // Start time
    ObjectNode startJson = objectMapper.createObjectNode();
    startJson.set(EmailConstants.DATE_TIME, TextNode.valueOf((String) inputMap.get(EmailConstants.MEETING_START_TIME)));
    startJson.set(EmailConstants.TIME_ZONE, TextNode.valueOf(timeZone));
    resultJson.set(EmailConstants.START, startJson);

    // End time
    ObjectNode endJson = objectMapper.createObjectNode();
    endJson.set(EmailConstants.DATE_TIME, TextNode.valueOf((String) inputMap.get(EmailConstants.MEETING_END_TIME)));
    endJson.set(EmailConstants.TIME_ZONE, TextNode.valueOf(timeZone));
    resultJson.set(EmailConstants.END, endJson);
    
    // Attendees
    ArrayNode attendeesJsonArray = objectMapper.createArrayNode();
    List<String> requiredAttendees = (List<String>) inputMap.get(EmailConstants.REQUIRED_ATTENDEES);
    List<String> optionalAttendees = new ArrayList<>();
    if (inputMap.containsKey(EmailConstants.OPTIONAL_ATTENDEES)) {
      optionalAttendees = (List<String>) inputMap.get(EmailConstants.OPTIONAL_ATTENDEES);
    }

    List<MeetingWrapper> meetingWrappers = new ArrayList<>();
    setAttendees(meetingWrappers, requiredAttendees, optionalAttendees);

    meetingWrappers.forEach((MeetingWrapper meetingWrapper) -> {
      ObjectNode attendeeJson = objectMapper.createObjectNode();
      ObjectNode emailAddressJson = objectMapper.createObjectNode();
      emailAddressJson.set(EmailConstants.ADDRESS, TextNode.valueOf(meetingWrapper.getEmail()));
      attendeeJson.set(EmailConstants.EMAIL_ADDRESS, emailAddressJson);
      attendeeJson.set(EmailConstants.TYPE, TextNode.valueOf(meetingWrapper.getType()));
      attendeesJsonArray.add(attendeeJson);
    });

    resultJson.set(EmailConstants.ATTENDEES, attendeesJsonArray);
    String meetingType = (String) inputMap.get(EmailConstants.MEETING_TYPE);

    if (meetingType != null && meetingType.equals(EmailConstants.TEAMS)) {
      resultJson.set(EmailConstants.IS_ONLINE_MEETING, BooleanNode.TRUE);
      resultJson.set(EmailConstants.ONLINE_MEETING_PROVIDER, TextNode.valueOf(EmailConstants.TEAMS_FOR_BUSINESS));
    }

    String location = (String) inputMap.get(EmailConstants.LOCATION);
    String locationUrl = (String) inputMap.get(EmailConstants.LOCATION_URL);
    if (location != null) {
      ObjectNode locationObj = objectMapper.createObjectNode();
      locationObj.set(EmailConstants.DISPLAY_NAME, TextNode.valueOf(location));
      Optional.ofNullable(locationUrl)
          .ifPresent((String url) -> locationObj.set(EmailConstants.LOCATION_URL, TextNode.valueOf(url)));
      resultJson.set(EmailConstants.LOCATION, locationObj);
    }
    // Add recurrence if present
    Object recurrenceObj = inputMap.get("recurrence");
    if (recurrenceObj instanceof Map) {
      log.debug("Recurrence object found: {}", recurrenceObj);
      ObjectNode recurrenceJson = objectMapper.convertValue(recurrenceObj, ObjectNode.class);
      resultJson.set("recurrence", recurrenceJson);
    }
    log.debug("Meeting request JSON: {}", resultJson);
    return resultJson.toString();
  }

  public static String convertToRescheduleMeetingPayload(Map<String, String> map) {
    ObjectNode meetingPayload = objectMapper.createObjectNode();

    // Start time object
    ObjectNode start = objectMapper.createObjectNode();
    start.set(EmailConstants.DATE_TIME, TextNode.valueOf(map.get(EmailConstants.START_TIME)));
    start.set(EmailConstants.TIME_ZONE, TextNode.valueOf(map.get(EmailConstants.TIME_ZONE)));

    // End time object
    ObjectNode end = objectMapper.createObjectNode();
    end.set(EmailConstants.DATE_TIME, TextNode.valueOf(map.get(EmailConstants.END_TIME)));
    end.set(EmailConstants.TIME_ZONE, TextNode.valueOf(map.get(EmailConstants.TIME_ZONE)));

    // Add start and end time to the main payload
    meetingPayload.set(EmailConstants.START, start);
    meetingPayload.set(EmailConstants.END, end);

    String rescheduleReason = map.get(EmailConstants.RESCHEDULE_REASON);
    if (rescheduleReason != null) {
      // Add body content (description)
      ObjectNode body = objectMapper.createObjectNode();
      body.set(EmailConstants.CONTENT_TYPE, TextNode.valueOf(EmailConstants.HTML)); // or "Text" for plain text
      body.set(EmailConstants.CONTENT, TextNode.valueOf(rescheduleReason)); // Fetching description from the map

      // Add body to the main payload
      meetingPayload.set(EmailConstants.BODY, body);
    }

    return meetingPayload.toString();
  }

  private static void setAttendees(
      List<MeetingWrapper> meetingWrappers,
      List<String> requiredAttendees,
      List<String> optionalAttendees) {
    requiredAttendees.forEach((String requiredAttendee) -> {
      meetingWrappers.add(setMeetingWrapper(requiredAttendee, EmailConstants.REQUIRED));
    });
    if (optionalAttendees != null) {
      optionalAttendees.forEach((String optionalAttendee) -> {
        meetingWrappers.add(setMeetingWrapper(optionalAttendee, EmailConstants.OPTIONAL));
      });
    }
  }

  private static MeetingWrapper setMeetingWrapper(String email, String type) {
    return new MeetingWrapper(email, type);
  }
}
