package com.enttribe.emailagent.utils;

import com.enttribe.emailagent.exception.BusinessException;
import com.enttribe.emailagent.userinfo.UserContextHolder;
import com.enttribe.emailagent.userinfo.UserInfo;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/** Token utility for handling multiple client credentials. */
@Service
@Slf4j
public class TokenUtils {

  @Value("${token.refresh.interval}")
  private long refreshInterval;

  @Value("${email.clients.info}")
  private String clientInfo;

  public static final String DEFAULT_CLIENT = "default";

  private final Map<String, ClientInfo> clients = new ConcurrentHashMap<>();

  // Token cache per client
  private final Map<String, TokenData> tokenCache = new ConcurrentHashMap<>();

  private static final int MAX_TOKEN_RETRIES = 5;
  private static final long TOKEN_RETRY_DELAY_MS = 5000L;
  private static final long TOKEN_EXPIRY_BUFFER_MS = 300000L; // 5 minutes
  private static final int HTTP_STATUS_OK = 200;
  private static final int HTTP_STATUS_REDIRECTION = 300;

  @PostConstruct
  public void init() {

    try {
      String decodedInfo = new String(Base64.getDecoder().decode(clientInfo));
      ObjectMapper mapper = JsonUtils.getObjectMapper();
      Map<String, ClientInfo> clientMap = mapper.readValue(decodedInfo, new TypeReference<>() {});

      // Step 1: Load and decrypt client info
      for (Map.Entry<String, ClientInfo> entry : clientMap.entrySet()) {
        String key = entry.getKey();
        ClientInfo entryValue = entry.getValue();
        try {
          log.info("decrypting the client credentials");
          String clientId = com.enttribe.commons.ai.util.AESUtils.decrypt(entryValue.clientId());
          String clientSecret = com.enttribe.commons.ai.util.AESUtils.decrypt(entryValue.clientSecret());
          String tenantId = com.enttribe.commons.ai.util.AESUtils.decrypt(entryValue.tenantId());
          log.info("Successfully decrypted the client credentials");
          entryValue = new ClientInfo(clientId, clientSecret, tenantId);
          clients.put(key, entryValue);
        } catch (Exception e) {
          log.error("error in decrypting credentials for client {} : {}", key, e.getMessage(), e);
          throw new BusinessException("Error in decrypting graph credentials");
        }
      }
      log.info("Successfully initialized and decrypted multiple clients.");
      // Step 2: Fetch access tokens for all clients
      for (String clientKey : clients.keySet()) {
        try {
          refreshToken(clientKey); // prefetch token
          log.info("Fetched token for clientKey: {}", clientKey);
        } catch (InterruptedException ie) {
          Thread.currentThread().interrupt();
          log.warn("Thread was interrupted while fetching calendar events", ie);
          throw new BusinessException("Thread interrupted while fetching calendar events", ie);
        } catch (Exception e) {
          log.warn("Could not fetch token for clientKey {} on init: {}", clientKey, e.getMessage());
        }
      }
    } catch (Exception e) {
      log.error("error in decrypting credentials for client : {}", e.getMessage());
      throw new BusinessException("Error in initializing token utility");
    }
  }

  public synchronized String getAccessToken(UserContextHolder userContextHolder)
      throws InterruptedException {
    UserInfo currentUser = userContextHolder.getCurrentUser();
    String clientKey = TokenUtils.DEFAULT_CLIENT;
    if (currentUser != null) {
      clientKey = currentUser.getCustomerName();
    }
    log.debug("accessing token for client : {}", clientKey);
    if (tokenCache.containsKey(clientKey)) {
      TokenData tokenData = tokenCache.get(clientKey);
      if (tokenData == null || Instant.now().isAfter(tokenData.expiryTime)) {
        log.info("Refreshing token for clientKey: {}", clientKey);
        refreshToken(clientKey);
      }
    } else {
      return tokenCache.get(TokenUtils.DEFAULT_CLIENT).accessToken;
    }
    return tokenCache.get(clientKey).accessToken;
  }

  public synchronized void refreshToken(String clientKey) throws InterruptedException {
    TokenData newTokenData = fetchNewAccessToken(clientKey);
    tokenCache.put(clientKey, newTokenData);
  }

  private TokenData fetchNewAccessToken(String clientKey) throws InterruptedException {
    ClientInfo clientInfo = clients.get(clientKey);
    if (clientInfo == null) {
      throw new IllegalArgumentException("No client info found for key: " + clientKey);
    }

    int maxRetries = MAX_TOKEN_RETRIES;
    int retryCount = 0;
    long retryDelay = TOKEN_RETRY_DELAY_MS;

    while (retryCount < maxRetries) {
      try (CloseableHttpClient httpClient = HttpClients.createDefault()) {

        HttpPost post =
            new HttpPost(
                "https://login.microsoftonline.com/" + clientInfo.tenantId + "/oauth2/v2.0/token");

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("client_id", clientInfo.clientId));
        params.add(new BasicNameValuePair("client_secret", clientInfo.clientSecret));
        params.add(new BasicNameValuePair("scope", "https://graph.microsoft.com/.default"));
        params.add(new BasicNameValuePair("grant_type", "client_credentials"));

        post.setEntity(new UrlEncodedFormEntity(params));
        post.setHeader("Content-Type", "application/x-www-form-urlencoded");

        try (CloseableHttpResponse response = httpClient.execute(post)) {
          int statusCode = response.getStatusLine().getStatusCode();
          String responseBody = EntityUtils.toString(response.getEntity());

          if (statusCode >= HTTP_STATUS_OK && statusCode < HTTP_STATUS_REDIRECTION) {
            JSONObject json = new JSONObject(responseBody);
            String accessToken = json.getString("access_token");
            return new TokenData(accessToken, Instant.now().plusMillis(refreshInterval + TOKEN_EXPIRY_BUFFER_MS));
          } else {
            throw new BusinessException(
                "Failed to retrieve token: HTTP " + statusCode + " - " + responseBody);
          }
        }
      } catch (Exception e) {
        retryCount++;
        log.error(
            "Attempt {} failed to get token for {}: {}", retryCount, clientKey, e.getMessage());
        if (retryCount < maxRetries) {
          Thread.sleep(retryDelay);
        } else {
          throw new BusinessException("Failed to get token after retries for: " + clientKey, e);
        }
      }
    }
    return null;
  }

  record ClientInfo(String clientId, String clientSecret, String tenantId) {}

  record TokenData(String accessToken, Instant expiryTime) {}
}
