package com.enttribe.emailagent.constant;

/**
 * Constants for API roles and permissions used throughout the email agent application. This class
 * defines the various role constants required for accessing different API endpoints.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0.0
 */
public final class APIConstants {

  // General constants
  public static final String DEFAULT = "default";

  // API Role constants
  public static final String ROLE_API_ACCEPT_MEETING = "ROLE_API_ACCEPT_MEETING";
  public static final String ROLE_API_CANCEL_EVENT = "ROLE_API_CANCEL_EVENT";
  public static final String ROLE_API_DECLINE_MEETING = "ROLE_API_DECLINE_MEETING";
  public static final String ROLE_API_FORWARD_EVENT = "ROLE_API_FORWARD_EVENT";
  public static final String ROLE_API_GET_AVAILABILITY = "ROLE_API_GET_AVAILABILITY";
  public static final String ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1 = "ROLE_API_GET_AVAILABLE_MEETING_SLOTS_V1";
  public static final String ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1 = "ROLE_API_GET_AVAILABLE_SLOTS_AND_CONFLICT_V1";
  public static final String ROLE_API_GET_CALENDAR_EVENTS = "ROLE_API_GET_CALENDAR_EVENTS";
  public static final String ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID = "ROLE_API_GET_CALENDAR_EVENT_BY_EVENT_ID";
  public static final String ROLE_API_GET_EVENT_BY_SUBJECT_AND_TIME = "ROLE_API_GET_EVENT_BY_SUBJECT_AND_TIME";
  public static final String ROLE_API_GET_EVENT_DETAILS = "ROLE_API_GET_EVENT_DETAILS";
  public static final String ROLE_API_GET_SCHEDULE = "ROLE_API_GET_SCHEDULE";
  public static final String ROLE_API_RESCHEDULE_EVENT = "ROLE_API_RESCHEDULE_EVENT";
  public static final String ROLE_API_SCHEDULE_EVENT = "ROLE_API_SCHEDULE_EVENT";
  public static final String ROLE_API_TENTATIVELY_ACCEPT_MEETING = "ROLE_API_TENTATIVELY_ACCEPT_MEETING";
  public static final String ROLE_API_UPDATE_EXISTING_EVENT = "ROLE_API_UPDATE_EXISTING_EVENT";
  public static final String ROLE_API_UPDATE_USER_DETAILS = "ROLE_API_UPDATE_USER_DETAILS";
  public static final String ROLE_API_CREATE_USER = "ROLE_API_CREATE_USER";
  public static final String ROLE_API_CREATE_USER_V1 = "ROLE_API_CREATE_USER_V1";
  public static final String ROLE_API_ASSIGN_LICENSE = "ROLE_API_ASSIGN_LICENSE";
  public static final String ROLE_API_SET_AUTOMATIC_REPLIES_SETTINGS = "ROLE_API_SET_AUTOMATIC_REPLIES_SETTINGS";

  // Private constructor to prevent instantiation
  private APIConstants() {
    throw new UnsupportedOperationException("utility class can not be instantiated");
  }
}
