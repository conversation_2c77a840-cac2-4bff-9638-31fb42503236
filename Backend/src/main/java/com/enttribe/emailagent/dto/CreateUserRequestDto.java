package com.enttribe.emailagent.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class CreateUserRequestDto {
    
    // Basic user information
    private String externalId;
    private String userName;
    private String givenName;
    private String familyName;
    private String displayName;
    private String nickName;
    private String userType;
    private String title;
    private String preferredLanguage;
    private String locale;
    private String timezone;
    private Boolean active;
    
    // Contact information
    private String primaryEmail;
    private String emailType;
    private List<PhoneNumberDto> phoneNumbers;
    private AddressDto address;
    
    // Enterprise extension
    private String employeeNumber;
    private String organization;
    private String department;
    private String division;
    private String costCenter;
    private String managerValue;
    
    // VisionWaves extension
    private String hireDate;
    private String jobCode;
    
    @Getter
    @Setter
    @NoArgsConstructor
    public static class PhoneNumberDto {
        private String value;
        private String type;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    public static class AddressDto {
        private String type;
        private String streetAddress;
        private String locality;
        private String region;
        private String postalCode;
        private String country;
        private Boolean primary;
    }
}
