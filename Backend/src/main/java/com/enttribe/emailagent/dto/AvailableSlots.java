package com.enttribe.emailagent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class AvailableSlots {

  @JsonProperty("availableSlots")
  private List<Meeting> slots; // Renamed field (formerly availableSlots)

  private List<ConflictMeetingWrapper> conflictMeeting;
  private List<OutOfOffice> outOfOffice;
  private Boolean filtered;
}
