<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.5</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.enttribe</groupId>
	<artifactId>emailagent</artifactId>
	<version>1.0.0</version>
	<name>Email-agent</name>
	<description>Demo project for Spring Boot</description>
	<url/>
	<licenses>
		<license/>
	</licenses>
	<developers>
		<developer/>
	</developers>
	<scm>
		<connection/>
		<developerConnection/>
		<tag/>
		<url/>
	</scm>
	<properties>
		<java.version>21</java.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-logging</artifactId>
		</dependency>
		<dependency>
			<groupId>com.enttribe.commons</groupId>
			<artifactId>ai</artifactId>
			<version>3.4.5</version>
			<exclusions>
				<exclusion>
					<groupId>io.micrometer</groupId>
					<artifactId>micrometer-tracing-bridge-otel</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.opentelemetry</groupId>
					<artifactId>opentelemetry-exporter-otlp</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.micrometer</groupId>
					<artifactId>micrometer-registry-otlp</artifactId>
				</exclusion>
				<exclusion>
					<groupId>io.micrometer</groupId>
					<artifactId>micrometer-registry-prometheus</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-lang3</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.18.0</version>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.mariadb.jdbc</groupId>
			<artifactId>mariadb-java-client</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>reposilite-repository-releases</id>
			<url>https://artifact.visionwaves.com/releases</url>
		</repository>
	</repositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.2.0</version>
				<executions>
					<execution>
						<id>copy-resources</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${project.build.directory}/docker</outputDirectory>
							<resources>
								<resource>
									<directory>src/main/resources</directory>
									<includes>
										<include>**/*</include>
									</includes>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- Copy JAR to target/docker after packaging -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<version>3.0.0</version>
				<executions>
					<execution>
						<id>copy-jar-to-docker</id>
						<phase>package</phase>
						<configuration>
							<target>
								<copy file="${project.build.directory}/${project.build.finalName}.jar" tofile="${project.build.directory}/docker/${project.build.finalName}.jar"/>
							</target>
						</configuration>
						<goals>
							<goal>run</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.cyclonedx</groupId>
				<artifactId>cyclonedx-maven-plugin</artifactId>
				<version>2.7.9</version>
				<configuration>
					<outputFormat>xml</outputFormat>
					<outputDirectory>${project.build.directory}</outputDirectory>
					<outputName>bom</outputName>
					<projectType>library</projectType>
					<schemaVersion>1.4</schemaVersion>
					<includeBomSerialNumber>true</includeBomSerialNumber>
					<includeCompileScope>true</includeCompileScope>
					<includeProvidedScope>true</includeProvidedScope>
					<includeRuntimeScope>true</includeRuntimeScope>
					<includeSystemScope>true</includeSystemScope>
					<includeTestScope>false</includeTestScope>
				</configuration>
				<executions>
					<execution>
						<id>create-bom</id>
						<phase>compile</phase>
						<goals>
							<goal>makeAggregateBom</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
